/// Flutter icons Anilist
/// Copyright (C) 2024 by original authors @ fluttericon.com, fontello.com
/// This font was generated by FlutterIcon.com, which is derived from Fontello.
///
/// To use this font, place it in your fonts/ directory and include the
/// following in your pubspec.yaml
///
/// flutter:
///   fonts:
///    - family:  Anilist
///      fonts:
///       - asset: fonts/Anilist.ttf
///
/// 
///
import 'package:flutter/widgets.dart';

class Anilist {
  Anilist._();

  static const _kFontFam = 'Anilist';
  static const String? _kFontPkg = null;

  static const IconData anilist = IconData(0xe800, fontFamily: _kFontFam, fontPackage: _kFontPkg);
}
