{"roots": ["unyo"], "packages": [{"name": "unyo", "version": "0.1.7", "dependencies": ["animated_snack_bar", "another_flutter_splash_screen", "archive", "bitsdojo_window", "build_runner", "cast", "collection", "crypto", "desktop_keep_screen_on", "easy_localization", "ffi", "ffigen", "flutter", "flutter_acrylic", "flutter_discord_rpc", "flutter_markdown", "flutter_window_close", "fluttericon", "fvp", "go_router", "hive", "html", "http", "icons_launcher", "image_gradient", "loading_animation_widget", "logger", "mqtt_client", "msix", "palette_generator", "path", "path_provider", "pie_chart", "shared_preferences", "shelf", "smooth_list_view", "smooth_video_progress", "url_launcher", "video_player"], "devDependencies": ["flutter_lints", "flutter_test"]}, {"name": "flutter_test", "version": "0.0.0", "dependencies": ["async", "boolean_selector", "characters", "clock", "collection", "fake_async", "flutter", "leak_tracker", "leak_tracker_flutter_testing", "leak_tracker_testing", "matcher", "material_color_utilities", "meta", "path", "source_span", "stack_trace", "stream_channel", "string_scanner", "term_glyph", "test_api", "vector_math", "vm_service"]}, {"name": "logger", "version": "2.5.0", "dependencies": []}, {"name": "ffigen", "version": "14.0.0", "dependencies": ["args", "cli_util", "collection", "ffi", "file", "glob", "logging", "package_config", "path", "pub_semver", "quiver", "yaml", "yaml_edit"]}, {"name": "ffi", "version": "2.1.3", "dependencies": []}, {"name": "flutter_discord_rpc", "version": "0.1.0+1", "dependencies": ["collection", "ffi", "flutter", "flutter_rust_bridge", "meta", "plugin_platform_interface", "uuid"]}, {"name": "cast", "version": "2.1.0", "dependencies": ["bonsoir", "flutter", "protobuf"]}, {"name": "fluttericon", "version": "2.0.0", "dependencies": ["flutter"]}, {"name": "path", "version": "1.9.1", "dependencies": []}, {"name": "hive", "version": "2.2.3", "dependencies": ["crypto", "meta"]}, {"name": "archive", "version": "4.0.5", "dependencies": ["crypto", "path", "posix"]}, {"name": "flutter_window_close", "version": "1.2.0", "dependencies": ["flutter", "flutter_web_plugins"]}, {"name": "path_provider", "version": "2.1.3", "dependencies": ["flutter", "path_provider_android", "path_provider_foundation", "path_provider_linux", "path_provider_platform_interface", "path_provider_windows"]}, {"name": "easy_localization", "version": "3.0.7+1", "dependencies": ["args", "easy_logger", "flutter", "flutter_localizations", "intl", "path", "shared_preferences"]}, {"name": "flutter_acrylic", "version": "1.1.4", "dependencies": ["flutter", "macos_window_utils"]}, {"name": "video_player", "version": "2.8.3", "dependencies": ["flutter", "html", "video_player_android", "video_player_avfoundation", "video_player_platform_interface", "video_player_web"]}, {"name": "fvp", "version": "0.31.2", "dependencies": ["ffi", "flutter", "http", "logging", "path", "path_provider", "plugin_platform_interface", "video_player", "video_player_platform_interface"]}, {"name": "flutter_markdown", "version": "0.7.7", "dependencies": ["flutter", "markdown", "meta", "path"]}, {"name": "crypto", "version": "3.0.3", "dependencies": ["typed_data"]}, {"name": "smooth_list_view", "version": "2.0.2", "dependencies": ["flutter"]}, {"name": "html", "version": "0.15.5+1", "dependencies": ["csslib", "source_span"]}, {"name": "palette_generator", "version": "0.3.3+6", "dependencies": ["collection", "flutter"]}, {"name": "pie_chart", "version": "5.4.0", "dependencies": ["flutter"]}, {"name": "animated_snack_bar", "version": "0.4.0", "dependencies": ["flutter"]}, {"name": "collection", "version": "1.19.1", "dependencies": []}, {"name": "loading_animation_widget", "version": "1.3.0", "dependencies": ["flutter"]}, {"name": "another_flutter_splash_screen", "version": "1.2.1", "dependencies": ["flutter"]}, {"name": "msix", "version": "3.16.7", "dependencies": ["args", "cli_util", "console", "get_it", "image", "package_config", "path", "pub_semver", "yaml"]}, {"name": "mqtt_client", "version": "10.2.1", "dependencies": ["crypto", "event_bus", "meta", "path", "typed_data", "universal_html"]}, {"name": "bitsdojo_window", "version": "0.1.6", "dependencies": ["bitsdojo_window_linux", "bitsdojo_window_macos", "bitsdojo_window_platform_interface", "bitsdojo_window_windows", "flutter"]}, {"name": "icons_launcher", "version": "2.1.7", "dependencies": ["args", "image", "path", "universal_io", "yaml"]}, {"name": "go_router", "version": "14.8.1", "dependencies": ["collection", "flutter", "flutter_web_plugins", "logging", "meta"]}, {"name": "build_runner", "version": "2.4.15", "dependencies": ["analyzer", "args", "async", "build", "build_config", "build_daemon", "build_resolvers", "build_runner_core", "code_builder", "collection", "crypto", "dart_style", "frontend_server_client", "glob", "graphs", "http", "http_multi_server", "io", "js", "logging", "meta", "mime", "package_config", "path", "pool", "pub_semver", "pubspec_parse", "shelf", "shelf_web_socket", "stack_trace", "stream_transform", "timing", "watcher", "web", "web_socket_channel", "yaml"]}, {"name": "desktop_keep_screen_on", "version": "0.0.3", "dependencies": ["dbus", "ffi", "flutter", "plugin_platform_interface", "synchronized"]}, {"name": "shared_preferences", "version": "2.5.3", "dependencies": ["flutter", "shared_preferences_android", "shared_preferences_foundation", "shared_preferences_linux", "shared_preferences_platform_interface", "shared_preferences_web", "shared_preferences_windows"]}, {"name": "shelf", "version": "1.4.2", "dependencies": ["async", "collection", "http_parser", "path", "stack_trace", "stream_channel"]}, {"name": "smooth_video_progress", "version": "0.0.4", "dependencies": ["flutter", "flutter_hooks", "video_player"]}, {"name": "image_gradient", "version": "0.0.2", "dependencies": ["flutter"]}, {"name": "url_launcher", "version": "6.2.6", "dependencies": ["flutter", "url_launcher_android", "url_launcher_ios", "url_launcher_linux", "url_launcher_macos", "url_launcher_platform_interface", "url_launcher_web", "url_launcher_windows"]}, {"name": "http", "version": "1.2.2", "dependencies": ["async", "http_parser", "meta", "web"]}, {"name": "flutter", "version": "0.0.0", "dependencies": ["characters", "collection", "material_color_utilities", "meta", "sky_engine", "vector_math"]}, {"name": "vm_service", "version": "15.0.0", "dependencies": []}, {"name": "vector_math", "version": "2.1.4", "dependencies": []}, {"name": "test_api", "version": "0.7.4", "dependencies": ["async", "boolean_selector", "collection", "meta", "source_span", "stack_trace", "stream_channel", "string_scanner", "term_glyph"]}, {"name": "term_glyph", "version": "1.2.2", "dependencies": []}, {"name": "string_scanner", "version": "1.4.1", "dependencies": ["source_span"]}, {"name": "stream_channel", "version": "2.1.4", "dependencies": ["async"]}, {"name": "stack_trace", "version": "1.12.1", "dependencies": ["path"]}, {"name": "source_span", "version": "1.10.1", "dependencies": ["collection", "path", "term_glyph"]}, {"name": "meta", "version": "1.16.0", "dependencies": []}, {"name": "material_color_utilities", "version": "0.11.1", "dependencies": ["collection"]}, {"name": "matcher", "version": "0.12.17", "dependencies": ["async", "meta", "stack_trace", "term_glyph", "test_api"]}, {"name": "leak_tracker_testing", "version": "3.0.1", "dependencies": ["leak_tracker", "matcher", "meta"]}, {"name": "leak_tracker_flutter_testing", "version": "3.0.9", "dependencies": ["flutter", "leak_tracker", "leak_tracker_testing", "matcher", "meta"]}, {"name": "leak_tracker", "version": "10.0.9", "dependencies": ["clock", "collection", "meta", "path", "vm_service"]}, {"name": "fake_async", "version": "1.3.3", "dependencies": ["clock", "collection"]}, {"name": "clock", "version": "1.1.2", "dependencies": []}, {"name": "characters", "version": "1.4.0", "dependencies": []}, {"name": "boolean_selector", "version": "2.1.2", "dependencies": ["source_span", "string_scanner"]}, {"name": "async", "version": "2.13.0", "dependencies": ["collection", "meta"]}, {"name": "plugin_platform_interface", "version": "2.1.8", "dependencies": ["meta"]}, {"name": "flutter_rust_bridge", "version": "1.82.6", "dependencies": ["args", "build_cli_annotations", "js", "meta", "path", "puppeteer", "shelf", "shelf_static", "shelf_web_socket", "tuple", "uuid", "web_socket_channel", "yaml"]}, {"name": "protobuf", "version": "3.1.0", "dependencies": ["collection", "fixnum", "meta"]}, {"name": "flutter_web_plugins", "version": "0.0.0", "dependencies": ["characters", "collection", "flutter", "material_color_utilities", "meta", "vector_math"]}, {"name": "flutter_localizations", "version": "0.0.0", "dependencies": ["characters", "clock", "collection", "flutter", "intl", "material_color_utilities", "meta", "path", "vector_math"]}, {"name": "intl", "version": "0.20.2", "dependencies": ["clock", "meta", "path"]}, {"name": "easy_logger", "version": "0.0.2", "dependencies": ["flutter"]}, {"name": "console", "version": "4.1.0", "dependencies": ["vector_math"]}, {"name": "universal_html", "version": "2.2.4", "dependencies": ["async", "charcode", "collection", "csslib", "html", "meta", "source_span", "typed_data", "universal_io"]}, {"name": "bitsdojo_window_windows", "version": "0.1.6", "dependencies": ["bitsdojo_window_platform_interface", "ffi", "flutter", "win32"]}, {"name": "bitsdojo_window_platform_interface", "version": "0.1.2", "dependencies": ["flutter", "meta", "plugin_platform_interface"]}, {"name": "bitsdojo_window_macos", "version": "0.1.4", "dependencies": ["bitsdojo_window_platform_interface", "ffi", "flutter"]}, {"name": "bitsdojo_window_linux", "version": "0.1.4", "dependencies": ["bitsdojo_window_platform_interface", "ffi", "flutter"]}, {"name": "universal_io", "version": "2.2.2", "dependencies": ["collection", "meta", "typed_data"]}, {"name": "build_runner_core", "version": "8.0.0", "dependencies": ["async", "build", "build_config", "build_resolvers", "collection", "convert", "crypto", "glob", "graphs", "json_annotation", "logging", "meta", "package_config", "path", "pool", "timing", "watcher", "yaml"]}, {"name": "sky_engine", "version": "0.0.0", "dependencies": []}, {"name": "build_cli_annotations", "version": "2.1.0", "dependencies": ["args", "meta"]}, {"name": "flutter_lints", "version": "3.0.2", "dependencies": ["lints"]}, {"name": "lints", "version": "3.0.0", "dependencies": []}, {"name": "yaml", "version": "3.1.3", "dependencies": ["collection", "source_span", "string_scanner"]}, {"name": "logging", "version": "1.3.0", "dependencies": []}, {"name": "file", "version": "7.0.1", "dependencies": ["meta", "path"]}, {"name": "path_provider_linux", "version": "2.2.1", "dependencies": ["ffi", "flutter", "path", "path_provider_platform_interface", "xdg_directories"]}, {"name": "typed_data", "version": "1.4.0", "dependencies": ["collection"]}, {"name": "csslib", "version": "1.0.2", "dependencies": ["source_span"]}, {"name": "event_bus", "version": "2.0.1", "dependencies": []}, {"name": "web", "version": "1.1.1", "dependencies": []}, {"name": "pool", "version": "1.5.1", "dependencies": ["async", "stack_trace"]}, {"name": "shared_preferences_windows", "version": "2.4.1", "dependencies": ["file", "flutter", "path", "path_provider_platform_interface", "path_provider_windows", "shared_preferences_platform_interface"]}, {"name": "shared_preferences_platform_interface", "version": "2.4.1", "dependencies": ["flutter", "plugin_platform_interface"]}, {"name": "shared_preferences_linux", "version": "2.4.1", "dependencies": ["file", "flutter", "path", "path_provider_linux", "path_provider_platform_interface", "shared_preferences_platform_interface"]}, {"name": "tuple", "version": "2.0.2", "dependencies": []}, {"name": "json_annotation", "version": "4.9.0", "dependencies": ["meta"]}, {"name": "pub_semver", "version": "2.2.0", "dependencies": ["collection"]}, {"name": "package_config", "version": "2.2.0", "dependencies": ["path"]}, {"name": "cli_util", "version": "0.4.2", "dependencies": ["meta", "path"]}, {"name": "uuid", "version": "4.5.1", "dependencies": ["crypto", "fixnum", "meta", "sprintf"]}, {"name": "sprintf", "version": "7.0.0", "dependencies": []}, {"name": "fixnum", "version": "1.1.1", "dependencies": []}, {"name": "posix", "version": "6.0.3", "dependencies": ["ffi", "meta", "path"]}, {"name": "path_provider_windows", "version": "2.3.0", "dependencies": ["ffi", "flutter", "path", "path_provider_platform_interface"]}, {"name": "path_provider_platform_interface", "version": "2.1.2", "dependencies": ["flutter", "platform", "plugin_platform_interface"]}, {"name": "path_provider_foundation", "version": "2.4.1", "dependencies": ["flutter", "path_provider_platform_interface"]}, {"name": "timing", "version": "1.0.2", "dependencies": ["json_annotation"]}, {"name": "shelf_web_socket", "version": "1.0.4", "dependencies": ["shelf", "stream_channel", "web_socket_channel"]}, {"name": "build_config", "version": "1.1.2", "dependencies": ["checked_yaml", "json_annotation", "path", "pubspec_parse", "yaml"]}, {"name": "build", "version": "2.4.2", "dependencies": ["analyzer", "async", "convert", "crypto", "glob", "logging", "meta", "package_config", "path"]}, {"name": "http_parser", "version": "4.1.2", "dependencies": ["collection", "source_span", "string_scanner", "typed_data"]}, {"name": "shelf_static", "version": "1.1.3", "dependencies": ["convert", "http_parser", "mime", "path", "shelf"]}, {"name": "args", "version": "2.7.0", "dependencies": []}, {"name": "stream_transform", "version": "2.1.1", "dependencies": []}, {"name": "js", "version": "0.6.7", "dependencies": ["meta"]}, {"name": "graphs", "version": "2.3.2", "dependencies": ["collection"]}, {"name": "frontend_server_client", "version": "4.0.0", "dependencies": ["async", "path"]}, {"name": "dbus", "version": "0.7.11", "dependencies": ["args", "ffi", "meta", "xml"]}, {"name": "shared_preferences_web", "version": "2.4.3", "dependencies": ["flutter", "flutter_web_plugins", "shared_preferences_platform_interface", "web"]}, {"name": "flutter_hooks", "version": "0.18.6", "dependencies": ["flutter"]}, {"name": "url_launcher_macos", "version": "3.2.2", "dependencies": ["flutter", "url_launcher_platform_interface"]}, {"name": "url_launcher_linux", "version": "3.2.1", "dependencies": ["flutter", "url_launcher_platform_interface"]}, {"name": "charcode", "version": "1.4.0", "dependencies": []}, {"name": "markdown", "version": "7.3.0", "dependencies": ["args", "meta"]}, {"name": "io", "version": "1.0.5", "dependencies": ["meta", "path", "string_scanner"]}, {"name": "http_multi_server", "version": "3.2.2", "dependencies": ["async"]}, {"name": "dart_style", "version": "3.1.0", "dependencies": ["analyzer", "args", "collection", "package_config", "path", "pub_semver", "source_span", "yaml"]}, {"name": "build_daemon", "version": "4.0.4", "dependencies": ["built_collection", "built_value", "crypto", "http_multi_server", "logging", "path", "pool", "shelf", "shelf_web_socket", "stream_transform", "watcher", "web_socket_channel"]}, {"name": "built_collection", "version": "5.1.1", "dependencies": []}, {"name": "shared_preferences_foundation", "version": "2.5.4", "dependencies": ["flutter", "shared_preferences_platform_interface"]}, {"name": "url_launcher_windows", "version": "3.1.4", "dependencies": ["flutter", "url_launcher_platform_interface"]}, {"name": "url_launcher_platform_interface", "version": "2.3.2", "dependencies": ["flutter", "plugin_platform_interface"]}, {"name": "checked_yaml", "version": "2.0.4", "dependencies": ["json_annotation", "source_span", "yaml"]}, {"name": "yaml_edit", "version": "2.2.2", "dependencies": ["collection", "meta", "source_span", "yaml"]}, {"name": "quiver", "version": "3.2.2", "dependencies": ["matcher"]}, {"name": "video_player_platform_interface", "version": "6.4.0", "dependencies": ["flutter", "plugin_platform_interface"]}, {"name": "web_socket_channel", "version": "2.4.0", "dependencies": ["async", "crypto", "stream_channel"]}, {"name": "watcher", "version": "1.1.2", "dependencies": ["async", "path"]}, {"name": "convert", "version": "3.1.2", "dependencies": ["typed_data"]}, {"name": "build_resolvers", "version": "2.4.4", "dependencies": ["analyzer", "async", "build", "collection", "convert", "crypto", "graphs", "logging", "package_config", "path", "pool", "pub_semver", "stream_transform", "yaml"]}, {"name": "url_launcher_ios", "version": "6.3.3", "dependencies": ["flutter", "url_launcher_platform_interface"]}, {"name": "glob", "version": "2.1.3", "dependencies": ["async", "collection", "file", "path", "string_scanner"]}, {"name": "mime", "version": "2.0.0", "dependencies": []}, {"name": "pubspec_parse", "version": "1.5.0", "dependencies": ["checked_yaml", "collection", "json_annotation", "pub_semver", "yaml"]}, {"name": "platform", "version": "3.1.6", "dependencies": []}, {"name": "code_builder", "version": "4.10.1", "dependencies": ["built_collection", "built_value", "collection", "matcher", "meta"]}, {"name": "url_launcher_web", "version": "2.4.1", "dependencies": ["flutter", "flutter_web_plugins", "url_launcher_platform_interface", "web"]}, {"name": "xdg_directories", "version": "1.1.0", "dependencies": ["meta", "path"]}, {"name": "xml", "version": "6.5.0", "dependencies": ["collection", "meta", "petitparser"]}, {"name": "petitparser", "version": "6.1.0", "dependencies": ["collection", "meta"]}, {"name": "shared_preferences_android", "version": "2.4.10", "dependencies": ["flutter", "shared_preferences_platform_interface"]}, {"name": "image", "version": "4.5.4", "dependencies": ["archive", "meta", "xml"]}, {"name": "analyzer", "version": "7.5.4", "dependencies": ["_fe_analyzer_shared", "collection", "convert", "crypto", "glob", "meta", "package_config", "path", "pub_semver", "source_span", "watcher", "yaml"]}, {"name": "_fe_analyzer_shared", "version": "85.0.0", "dependencies": ["meta"]}, {"name": "macos_window_utils", "version": "1.8.4", "dependencies": ["flutter"]}, {"name": "synchronized", "version": "3.4.0", "dependencies": []}, {"name": "get_it", "version": "7.7.0", "dependencies": ["async", "collection", "meta"]}, {"name": "path_provider_android", "version": "2.2.17", "dependencies": ["flutter", "path_provider_platform_interface"]}, {"name": "video_player_avfoundation", "version": "2.7.2", "dependencies": ["flutter", "video_player_platform_interface"]}, {"name": "puppeteer", "version": "3.18.0", "dependencies": ["archive", "async", "collection", "http", "logging", "path", "petitparser", "pool", "web"]}, {"name": "url_launcher_android", "version": "6.3.16", "dependencies": ["flutter", "url_launcher_platform_interface"]}, {"name": "bonsoir", "version": "5.1.11", "dependencies": ["bonsoir_android", "bonsoir_darwin", "bonsoir_linux", "bonsoir_platform_interface", "bonsoir_windows", "flutter"]}, {"name": "bonsoir_windows", "version": "5.1.5", "dependencies": ["bonsoir_platform_interface", "flutter"]}, {"name": "bonsoir_platform_interface", "version": "5.1.3", "dependencies": ["flutter", "meta", "plugin_platform_interface"]}, {"name": "bonsoir_linux", "version": "5.1.3", "dependencies": ["bonsoir_platform_interface", "dbus", "flutter", "meta"]}, {"name": "bonsoir_darwin", "version": "5.1.3", "dependencies": ["bonsoir_platform_interface", "flutter"]}, {"name": "bonsoir_android", "version": "5.1.6", "dependencies": ["bonsoir_platform_interface", "flutter"]}, {"name": "win32", "version": "5.13.0", "dependencies": ["ffi"]}, {"name": "video_player_web", "version": "2.3.5", "dependencies": ["flutter", "flutter_web_plugins", "video_player_platform_interface", "web"]}, {"name": "built_value", "version": "8.10.1", "dependencies": ["built_collection", "collection", "fixnum", "meta"]}, {"name": "video_player_android", "version": "2.8.7", "dependencies": ["flutter", "video_player_platform_interface"]}], "configVersion": 1}