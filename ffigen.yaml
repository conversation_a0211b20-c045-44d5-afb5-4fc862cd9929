# Run with `flutter pub run ffigen --config ffigen.yaml`.
name: TorrentLibrary
description: Bindings to `lib/ffi/libmtorrentserver.h`.
output: 'lib/ffi/generated_bindings.dart'
headers:
  entry-points:
    - 'ffigen/libmtorrentserver.h'
preamble: |
  // ignore_for_file: always_specify_types
  // ignore_for_file: camel_case_types
  // ignore_for_file: non_constant_identifier_names
  // ignore_for_file: unused_field
  // ignore_for_file: unused_element
comments:
  style: any
  length: full
