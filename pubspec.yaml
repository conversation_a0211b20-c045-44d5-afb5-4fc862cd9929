name: unyo
description: "An anime app."
# The following line prevents the package from being accidentally published to
# pub.dev using `flutter pub publish`. This is preferred for private packages.
publish_to: 'none' # Remove this line if you wish to publish to pub.dev

version: 0.1.7

environment:
  sdk: '>=3.3.1 <4.0.0'
  flutter: '>=3.16.6'

dependencies:
  flutter:
    sdk: flutter

  http: 1.2.2
  url_launcher: 6.2.6
  image_gradient: 0.0.2
  smooth_video_progress: 0.0.4
  shelf: 1.4.2
  shared_preferences: 2.5.3
  desktop_keep_screen_on: 0.0.3
  build_runner: 2.4.15
  go_router: 14.8.1
  icons_launcher: 2.1.7
  bitsdojo_window: 0.1.6
  mqtt_client: 10.2.1
  msix: 3.16.7
  another_flutter_splash_screen: 1.2.1
  loading_animation_widget: 1.3.0
  collection: 1.19.1
  animated_snack_bar: 0.4.0
  pie_chart: 5.4.0
  palette_generator: 0.3.3+6
  html: 0.15.5+1
  smooth_list_view: 2.0.2
  crypto: 3.0.3
  flutter_markdown: 0.7.7
  fvp: 0.31.2
  video_player: 2.8.3
  flutter_acrylic: 1.1.4
  easy_localization: 3.0.7+1
  path_provider: 2.1.3
  flutter_window_close: 1.2.0
  archive: 4.0.5
  hive: 2.2.3
  path: ^1.9.1
  fluttericon: 2.0.0
  cast: 2.1.0
  flutter_discord_rpc: 0.1.0+1
  ffi: 2.1.3
  ffigen: 14.0.0
  logger: 2.5.0

icons_launcher:
  image_path: 'assets/logo.png'
  platforms:
    macos:
      enable: true
      image_path: 'assets/logo.png'
    windows:
      enable: true
      image_path: 'assets/logo.png'
    linux:
      enable: true
      image_path: 'assets/logo.png'

msix_config:
  display_name: Unyo
  publisher_display_name: K3vinb5
  logo_path: ./assets/logo.png


dev_dependencies:
  flutter_test:
    sdk: flutter

  flutter_lints: ^3.0.1
  #dmg: ^0.0.5

# For information on the generic Dart part of this file, see the
# following page: https://dart.dev/tools/pub/pubspec

# The following section is specific to Flutter packages.
flutter:

  # The following line ensures that the Material Icons font is
  # included with your application, so that you can use the icons in
  # the material Icons class.
  uses-material-design: true

  # To add assets to your application, add an assets section, like this:
  assets:
    - assets/logo.png
    - assets/languages/
    - assets/extensions.jar
    # - assets/embedded-api-linux
  #   - images/a_dot_ham.jpeg

  fonts:
   - family: Anilist
     fonts:
       - asset: fonts/Anilist.ttf

  # An image asset can refer to one or more resolution-specific "variants", see
  # https://flutter.dev/assets-and-images/#resolution-aware

  # For details regarding adding assets from package dependencies, see
  # https://flutter.dev/assets-and-images/#from-packages

  # For details regarding fonts from package dependencies,
  # see https://flutter.dev/custom-fonts/#from-packages


