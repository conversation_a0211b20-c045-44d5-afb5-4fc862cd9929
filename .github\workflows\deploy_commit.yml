name: Release Branch or Commit

on:
  workflow_dispatch:
    inputs:
      ref:
        description: 'Branch or commit SHA to release'
        required: true
        default: 'master'
      version:
        description: 'Version to release'
        required: true

jobs:
  create-tag:
    runs-on: ubuntu-latest
    steps:
      - name: Checkout code
        uses: actions/checkout@v4
      - name: Create Git Tag
        id: create_tag
        uses: actions/github-script@v6
        with:
          script: |
            const tagName = `v${{ github.event.inputs.version }}`;
            const { data: tag } = await github.rest.git.createRef({
              owner: context.repo.owner,
              repo: context.repo.repo,
              ref: `refs/tags/${tagName}`,
              sha: context.sha,
            });
            return tag;;

  build-and-release-linux:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4
        with:
          ref: ${{ github.event.inputs.ref }}
      - uses: subosito/flutter-action@v2
        with:
          channel: 'stable'
          flutter-version: '3.19.5'
      - uses: actions/setup-go@v5
        with:
          go-version: '1.23.1'
      - name: apt update
        run: sudo apt-get update
      - name: Install dependencies
        run: sudo apt-get install -y clang cmake ninja-build pkg-config libgtk-3-dev liblzma-dev locate libfuse2
      - name: Install project dependencies
        run: flutter pub get
      - name: Generate intermediates
        run: flutter pub run build_runner build --delete-conflicting-outputs
      - name: Enable linux build
        run: flutter config --enable-linux-desktop
      - name: Build artifacts
        run: flutter build linux --release
      - name: Build go dependencies
        run: |
          mkdir -p linux/bundle/lib
          cd go/binding/desktop
          go build -o ../../../build/linux/x64/release/bundle/lib/libmtorrentserver.so -buildmode=c-shared main.go
          cd ../../..
      - name: Archive app
        uses: thedoctor0/zip-release@master
        with:
          type: 'zip'
          filename: Unyo-${{ github.event.inputs.version }}-linux.zip
          directory: build/linux/x64/release/bundle
      - name: Linux Archive Release
        uses: softprops/action-gh-release@v1
        env:
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
        with:
          files: build/linux/x64/release/bundle/Unyo-${{ github.event.inputs.version }}-linux.zip
      - name: Copy AppDir
        run: cp -r linux-appimage/Unyo.AppDir .
      - name: Copy Compiled App
        run: cp -r build/linux/x64/release/bundle/* Unyo.AppDir/.
      - name: Copy appimagetool
        run: cp linux-appimage/appimagetool.AppImage .
      - name: Give Permissions
        run: chmod 755 appimagetool.AppImage
      - name: Execute appimagetool
        run: ./appimagetool.AppImage Unyo.AppDir Unyo-${{ github.event.inputs.version }}-linux.AppImage
      - name: Linux AppImage Release
        uses: softprops/action-gh-release@v1
        env:
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
        with:
          files: /home/<USER>/work/Unyo/Unyo/Unyo-${{ github.event.inputs.version }}-linux.AppImage
      - name: Activate flutter_to_debian
        run: dart pub global activate flutter_to_debian
      - name: Add Path
        run: export PATH="$PATH":"$HOME/.pub-cache/bin"
      - name: Build deb package
        run: flutter_to_debian
      - name: Rename deb file
        run: cp build/linux/x64/release/debian/unyo_0.0.0_amd64.deb build/linux/x64/release/debian/unyo-${{ github.event.inputs.version }}-linux.deb
      - name: Linux Debian package Release
        uses: softprops/action-gh-release@v1
        env:
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
        with:
          files: build/linux/x64/release/debian/unyo-${{ github.event.inputs.version }}-linux.deb

  build-and-release-windows:
    runs-on: windows-latest
    steps:
      - uses: actions/checkout@v4
        with:
          ref: ${{ github.event.inputs.ref }}
      - uses: subosito/flutter-action@v2
        with:
          channel: 'stable'
          flutter-version: '3.19.5'
      - uses: actions/setup-go@v5
        with:
          go-version: '1.23.1'
      - name: Install project dependencies
        run: flutter pub get
      - name: Generate intermediates
        run: flutter pub run build_runner build --delete-conflicting-outputs
      - name: Enable windows build
        run: flutter config --enable-windows-desktop
      - name: Build artifacts
        run: flutter build windows --release
      - name: Build go dependencies
        run: |
          cd go\binding\desktop
          go build -o ..\..\..\build\windows\x64\runner\Release\libmtorrentserver.dll -buildmode=c-shared main.go
          cd ..\..\..
      - name: Archive App
        uses: thedoctor0/zip-release@master
        with:
          type: 'zip'
          filename: Unyo-${{ github.event.inputs.version }}-windows.zip
          directory: build\windows\x64\runner\Release
      - name: Windows Archive Release
        uses: softprops/action-gh-release@v1
        env:
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
        with:
          files: build/windows/x64/runner/Release/Unyo-${{ github.event.inputs.version }}-windows.zip
      - name: Running installer build
        uses: Minionguyjpro/Inno-Setup-Action@v1.2.4
        with:
          path: .\windows-inno-script.iss
          options: /O+ /dMyAppVersion=${{ github.event.inputs.version }}
      - name: Windows Exe Release
        uses: softprops/action-gh-release@v1
        env:
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
        with:
          files: build\windows\x64\runner\unyo-${{ github.event.inputs.version }}-windows-setup.exe

  build-and-release-macos:
    runs-on: macos-14
    steps:
      - uses: actions/checkout@v4
        with:
          ref: ${{ github.event.inputs.ref }}
      - uses: subosito/flutter-action@v2
        with:
          channel: 'stable'
          flutter-version: '3.19.5'
      - uses: actions/setup-go@v5
        with:
          go-version: '1.23.1'
      - name: Install project dependencies
        run: flutter pub get
      - name: Generate intermediates
        run: flutter pub run build_runner build --delete-conflicting-outputs
      - name: Enable macOS build
        run: flutter config --enable-macos-desktop
      - name: Build artifacts
        run: flutter build macos --release
      - name: Build go dependencies
        run: |
          mkdir -p macos/Frameworks
          cd go/binding/desktop
          go build -o ../../../build/macos/Build/Products/Release/Frameworks/libmtorrentserver.dylib -buildmode=c-shared main.go
          cd ../../..
      - name: Archive App
        uses: thedoctor0/zip-release@master
        with:
          type: 'zip'
          filename: Unyo-${{ github.event.inputs.version }}-macos.zip
          directory: build/macos/Build/Products/Release
      - name: macOS Archive Release
        uses: softprops/action-gh-release@v1
        env:
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
        with:
          files: build/macos/Build/Products/Release/Unyo-${{ github.event.inputs.version }}-macos.zip
      - name: build macos
        run: |
          cd macos
          pod update
          cd ..
          brew install create-dmg
          create-dmg --volname Unyo-${{ github.event.inputs.version }}-macos --window-pos 200 120 --window-size 800 450 --icon-size 100 --app-drop-link 600 185 Unyo-${{ github.event.inputs.version }}-macos.dmg build/macos/Build/Products/Release/unyo.app
      - name: macOS dmg Release
        uses: softprops/action-gh-release@v1
        env:
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
        with:
          files: /Users/<USER>/work/Unyo/Unyo/Unyo-${{ github.event.inputs.version }}-macos.dmg