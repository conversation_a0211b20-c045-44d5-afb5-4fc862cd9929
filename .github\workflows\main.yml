name: Flutter CI

on: push

jobs:

  build-and-release-linux:
    runs-on: ubuntu-latest
    if: github.ref_type == 'tag'
    permissions:
      contents: write
    steps:
      - uses: actions/checkout@v4
      - uses: subosito/flutter-action@v2
        with:
          channel: 'stable'
          flutter-version: '3.19.5'
      - uses: actions/setup-go@v5
        with:
          go-version: '1.23.1'
      - name: apt update
        run: sudo apt-get update
      - name: Install dependencies
        run: sudo apt-get install -y clang cmake ninja-build pkg-config libgtk-3-dev liblzma-dev locate libfuse2
      - name: Install project dependencies
        run: flutter pub get 
      - name: Generate intermediates
        run: flutter pub run build_runner build --delete-conflicting-outputs
      # - uses: graalvm/setup-graalvm@v1
      #   with:
      #     java-version: '21'
      #     distribution: 'graalvm'
      #     github-token: ${{ secrets.GITHUB_TOKEN }}
      #     native-image-job-reports: 'true'
      # - name: Generate embedded native server
      #   run: |
      #     cp assets/api-0.0.2.jar .
      #     native-image -jar  api-0.0.2.jar embedded-api
      #     cp embedded-api assets/.
      - name: Enable linux build
        run: flutter config --enable-linux-desktop
      - name: Build artifacts
        run: flutter build linux --release
      - name: Build go dependencies
        run: |
          mkdir -p linux/bundle/lib
          cd go/binding/desktop
          go build -o ../../../build/linux/x64/release/bundle/lib/libmtorrentserver.so -buildmode=c-shared main.go
          cd ../../..
      - name: Archive app
        uses: thedoctor0/zip-release@master
        with:
          type: 'zip'
          filename: Unyo-${{github.ref_name}}-linux.zip
          directory: build/linux/x64/release/bundle
      - name: Linux Archive Release
        uses: softprops/action-gh-release@v1
        env:
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
        with:
          files: build/linux/x64/release/bundle/Unyo-${{github.ref_name}}-linux.zip
      - name: Copy AppDir
        run: cp -r linux-appimage/Unyo.AppDir .
      - name: Copy Compiled App
        run: cp -r build/linux/x64/release/bundle/* Unyo.AppDir/.
      - name: Copy appimagetool
        run: cp linux-appimage/appimagetool.AppImage .
      - name: Give Permissions
        run: chmod 755 appimagetool.AppImage
      - name: Execute appimagetool
        run: ./appimagetool.AppImage Unyo.AppDir Unyo-${{github.ref_name}}-linux.AppImage
      - name: Linux AppImage Release
        uses: softprops/action-gh-release@v1
        env:
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
        with:
          files: /home/<USER>/work/Unyo/Unyo/Unyo-${{github.ref_name}}-linux.AppImage
      - name: Activate flutter_to_debian
        run: dart pub global activate flutter_to_debian
      - name: Add Path
        run: export PATH="$PATH":"$HOME/.pub-cache/bin"
      - name: Build deb package
        run: flutter_to_debian
      - name: Rename deb file
        run: cp build/linux/x64/release/debian/unyo_0.0.0_amd64.deb build/linux/x64/release/debian/unyo-${{github.ref_name}}-linux.deb
      - name: Linux Debain package Release
        uses: softprops/action-gh-release@v1
        env:
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
        with:
          files: build/linux/x64/release/debian/unyo-${{github.ref_name}}-linux.deb

  build-and-release-windows:
    runs-on: windows-latest
    if: github.ref_type == 'tag'
    permissions:
      contents: write
    steps:
      - uses: actions/checkout@v4
      - uses: subosito/flutter-action@v2
        with:
          channel: 'stable'
          flutter-version: '3.19.5'
      - uses: actions/setup-go@v5
        with:
          go-version: '1.23.1'
      - name: Install project dependencies
        run: flutter pub get 
      - name: Generate intermediates
        run: flutter pub run build_runner build --delete-conflicting-outputs
      - name: Enable windows builwindowsd
        run: flutter config --enable-windows-desktop
      - name: Build artifacts
        run: flutter build windows --release
      - name: Build go dependencies
        run: |
          cd go\binding\desktop
          go build -o ..\..\..\build\windows\x64\runner\Release\libmtorrentserver.dll -buildmode=c-shared main.go
          cd ..\..\..
      - name: Archive App
        uses: thedoctor0/zip-release@master
        with:
          type: 'zip'
          filename: Unyo-${{github.ref_name}}-windows.zip
          directory: build\windows\x64\runner\Release
      - name: Windows Archive Release
        uses: softprops/action-gh-release@v1
        env:
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
        with:
          files: build/windows/x64/runner/Release/Unyo-${{github.ref_name}}-windows.zip
      - name: Running installer build
        uses: Minionguyjpro/Inno-Setup-Action@v1.2.4
        with:
          path: .\windows-inno-script.iss
          options: /O+ /dMyAppVersion=${{github.ref_name}}
      - name: Windows Exe Release
        uses: softprops/action-gh-release@v1
        env:
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
        with:
          files: build\windows\x64\runner\unyo-${{github.ref_name}}-windows-setup.exe

  build-and-release-macos:
    runs-on: macos-14
    if: github.ref_type == 'tag'
    permissions:
      contents: write
    steps:
      - uses: actions/checkout@v4
      - uses: subosito/flutter-action@v2
        with:
          channel: 'stable'
          flutter-version: '3.19.5'
      - uses: actions/setup-go@v5
        with:
          go-version: '1.23.1'
      - name: Install project dependencies
        run: flutter pub get 
      - name: Generate intermediates
        run: flutter pub run build_runner build --delete-conflicting-outputs
      - name: Enable macOS build
        run: flutter config --enable-macos-desktop
      - name: Build artifacts
        run: flutter build macos --release
      - name: Build go dependencies
        run: |
          mkdir -p macos/Frameworks
          cd go/binding/desktop
          go build -o ../../../build/macos/Build/Products/Release/Frameworks/libmtorrentserver.dylib -buildmode=c-shared main.go
          cd ../../..
      - name: Archive App
        uses: thedoctor0/zip-release@master
        with:
          type: 'zip'
          filename: Unyo-${{github.ref_name}}-macos.zip
          directory: build/macos/Build/Products/Release
      - name: macOS Archive Release
        uses: softprops/action-gh-release@v1
        env:
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
        with:
          files: build/macos/Build/Products/Release/Unyo-${{github.ref_name}}-macos.zip
      - name: build macos
        run: |
          cd macos
          pod update
          cd ..
          brew install create-dmg 
          create-dmg --volname Unyo-${{ github.ref_name }}-macos --window-pos 200 120 --window-size 800 450 --icon-size 100 --app-drop-link 600 185 Unyo-${{ github.ref_name }}-macos.dmg build/macos/Build/Products/Release/unyo.app
      - name: macOS dmg Release
        uses: softprops/action-gh-release@v1
        env:
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
        with:
          files: /Users/<USER>/work/Unyo/Unyo/Unyo-${{github.ref_name}}-macos.dmg

