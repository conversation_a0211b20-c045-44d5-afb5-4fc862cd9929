{"entries": "entries", "continue_watching": "Continue Watching", "continue_reading": "Continue Reading", "episodes_watched": "Episodes Watched", "hours_watched": "Hours Watched", "update_progress_automatically": "Update progress automatically", "buttons_layout_menu_layout": "Buttons Layout / Menu Layout", "select_language": "Select Language", "video_duration_remaining_time": "Display video duration / Display remaining time", "select_theme": "Select Theme", "home": "Home", "anime_list": "Anime List", "manga_list": "Manga List", "calendar": "Calendar", "anime": "Anime", "manga": "Manga", "recently_released": "Recently Released", "search": "Search...", "trending": "Trending", "season_popular": "Season Popular", "advanced_search": "Advanced Search", "select_format": "Select Format", "select_season": "Select Season", "select_year": "Select Year", "select_sorting": "Select Sorting", "releasing": "Releasing", "unreleased": "Unreleased", "finished": "Finished", "episodes": "Episodes", "chapters": "Chapters", "wrong_no_title": "Wrong/No Title?", "update_entry": "Update Entry", "status": "Status", "progress": "Progress", "score": "Score", "start_end_data": "Start/End Date", "confirm": "Confirm", "cancel": "Cancel", "list_editor": "List Editor", "select_title": "Select Title", "select_new_title_text": "Please Select for new title or search for new one", "yearly_popular": "Yearly Popular", "monday": "Monday", "tuesday": "Tuesday", "wednesday": "Wednesday", "thursday": "Thursday", "friday": "Friday", "saturday": "Saturday", "sunday": "Sunday", "not_yet_released": "Not yet released", "show_adult_content": "Show Adult Content", "please_wait_text": "Please wait, this can take some seconds...", "select_quality": "Select quality", "error_occured_video_title": "An error occured D:", "error_occured_video_text": "An error occured, try using another source or server/quality", "quality_no_results": "The source returned no results, please try another source or server/quality :D", "released": "Released", "episode": "Episode", "chapter": "Chapter", "settings": "Settings", "remote_endpoint": "Remote extensions / Local extensions", "local_extensions": "Local Extensions", "extension": "extension", "extensions": "Extensions", "change_repo": "Change repo", "change_repo_message": "Please paste on the textfield the new repo link and confirm if you wish to use another repository", "need_help": "Need help?", "need_help_title": "Here is some help and information you're probably needing :D", "need_help_message": "Some of you are probably wondering, what are this local extensions? Basically instead of the sources being hosted online on Unyo's server you have them on you computer. What are the advantages you ask, because the server is located in germany some extensions don't work, while if you use local extensions they will work fine dependending on your country, other advantages are, if you have local extensions enable you don't have to see a long list with all the remote extensions, here you can have just the extensions you use and need, you can also code your extenions yourself, but don't worry, the main repo is activelly developed and more extensions will be added with time. Now go ahead and install some extensions in the Local Extensions page :D", "no_extensions_title": "You have no installed extensions", "no_extensions_message": "If you're seeing this it means you have turned on the local extensions but you have no installed extensions, you can install some in the Local Extensions page :D", "unyo2gether_message": "Please paste your buddys peerId, or all of you come up with a new one!", "extensions_not_enabled_message": "Local Extensions are not enabled, you can enable them on the settings", "installed": "Installed", "console": "Extensions Server Console", "previous_episode": "Previous Episode", "next_episode": "Next Episode", "play": "Play", "pause": "Pause", "change_audiotrack": "Change Audiotrack", "change_subtitles": "Change Subtitles", "enter_fullscreen": "Enter Fullscreen", "exit_fullscreen": "Exit Fullscreen", "connected": "Connected", "not_connected": "Not Connected", "delete": "Delete", "download": "Download", "exit_fullscreen_on_video_exit": "Exit fullscreen on video exit", "resume_from": "Resume from", "search_from_website": "Search your title from the website here", "current_selection": "Current selection", "skip_opening_automatically": "Skip opening automatically (when available)", "select_intro_skip_time": "Select default manual intro skip time", "select_default_title_type": "Select default title type", "change_playback_speed": "Change playback speed", "select_episode_completed_percentage": "Select percentage at which an episode is marked as completed", "select_chapter_completed_percentage": "Select percentage at which a chapter is marked as completed", "enable_open_subtitles": "Enable opensubtitles.org subtitles (when available/found)", "no_title_found_dialog": "No title was found, try the Wrong/No Title menu or another source", "page": "Page", "show_chapters": "Show chapters", "single_page": "Single Page", "double_page": "Double Page", "long_strip": "Long Strip", "left_to_right": "Left to Right", "right_to_left": "Right to Left", "light_mode": "Light Mode", "dark_mode": "Dark Mode", "fit_height": "Fit Height", "fit_width": "<PERSON><PERSON>", "logout_title": "Do you wanna log out?", "logout_text": "Are you sure you want to log out?", "select_user": "Who's <PERSON>?", "restore_default": "Restore default", "create_local_account": "Create Local Account", "create_local_account_title": "New Account", "add_account": "Add Account", "insert_new_name": "Insert username", "sign_int_title": "Add a new account", "previous_chapter": "Previous Chapter", "next_chapter": "Next Chapter", "next_page": "Next Page", "previous_page": "Previous Page", "video_loading": "Please wait, the video is loading", "cast_to_tv": "Cast to TV", "enable_discord_rpc": "Discord RPC"}