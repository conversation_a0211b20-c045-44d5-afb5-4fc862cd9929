PODS:
  - bitsdojo_window_macos (0.0.1):
    - FlutterMacOS
  - bonsoir_darwin (0.0.1):
    - Flutter
    - FlutterMacOS
  - desktop_keep_screen_on (0.0.1):
    - FlutterMacOS
  - flutter_discord_rpc (0.0.1)
  - flutter_window_close (0.0.1):
    - FlutterMacOS
  - FlutterMacOS (1.0.0)
  - fvp (0.31.2):
    - Flutter
    - FlutterMacOS
    - mdk (~> 0.32.0)
  - macos_window_utils (1.0.0):
    - FlutterMacOS
  - mdk (0.32.0)
  - path_provider_foundation (0.0.1):
    - Flutter
    - FlutterMacOS
  - shared_preferences_foundation (0.0.1):
    - Flutter
    - FlutterMacOS
  - url_launcher_macos (0.0.1):
    - FlutterMacOS
  - video_player_avfoundation (0.0.1):
    - Flutter
    - FlutterMacOS

DEPENDENCIES:
  - bitsdojo_window_macos (from `Flutter/ephemeral/.symlinks/plugins/bitsdojo_window_macos/macos`)
  - bonsoir_darwin (from `Flutter/ephemeral/.symlinks/plugins/bonsoir_darwin/darwin`)
  - desktop_keep_screen_on (from `Flutter/ephemeral/.symlinks/plugins/desktop_keep_screen_on/macos`)
  - flutter_discord_rpc (from `Flutter/ephemeral/.symlinks/plugins/flutter_discord_rpc/macos`)
  - flutter_window_close (from `Flutter/ephemeral/.symlinks/plugins/flutter_window_close/macos`)
  - FlutterMacOS (from `Flutter/ephemeral`)
  - fvp (from `Flutter/ephemeral/.symlinks/plugins/fvp/darwin`)
  - macos_window_utils (from `Flutter/ephemeral/.symlinks/plugins/macos_window_utils/macos`)
  - path_provider_foundation (from `Flutter/ephemeral/.symlinks/plugins/path_provider_foundation/darwin`)
  - shared_preferences_foundation (from `Flutter/ephemeral/.symlinks/plugins/shared_preferences_foundation/darwin`)
  - url_launcher_macos (from `Flutter/ephemeral/.symlinks/plugins/url_launcher_macos/macos`)
  - video_player_avfoundation (from `Flutter/ephemeral/.symlinks/plugins/video_player_avfoundation/darwin`)

SPEC REPOS:
  trunk:
    - mdk

EXTERNAL SOURCES:
  bitsdojo_window_macos:
    :path: Flutter/ephemeral/.symlinks/plugins/bitsdojo_window_macos/macos
  bonsoir_darwin:
    :path: Flutter/ephemeral/.symlinks/plugins/bonsoir_darwin/darwin
  desktop_keep_screen_on:
    :path: Flutter/ephemeral/.symlinks/plugins/desktop_keep_screen_on/macos
  flutter_discord_rpc:
    :path: Flutter/ephemeral/.symlinks/plugins/flutter_discord_rpc/macos
  flutter_window_close:
    :path: Flutter/ephemeral/.symlinks/plugins/flutter_window_close/macos
  FlutterMacOS:
    :path: Flutter/ephemeral
  fvp:
    :path: Flutter/ephemeral/.symlinks/plugins/fvp/darwin
  macos_window_utils:
    :path: Flutter/ephemeral/.symlinks/plugins/macos_window_utils/macos
  path_provider_foundation:
    :path: Flutter/ephemeral/.symlinks/plugins/path_provider_foundation/darwin
  shared_preferences_foundation:
    :path: Flutter/ephemeral/.symlinks/plugins/shared_preferences_foundation/darwin
  url_launcher_macos:
    :path: Flutter/ephemeral/.symlinks/plugins/url_launcher_macos/macos
  video_player_avfoundation:
    :path: Flutter/ephemeral/.symlinks/plugins/video_player_avfoundation/darwin

SPEC CHECKSUMS:
  bitsdojo_window_macos: 7959fb0ca65a3ccda30095c181ecb856fae48ea9
  bonsoir_darwin: 29c7ccf356646118844721f36e1de4b61f6cbd0e
  desktop_keep_screen_on: bfedcf75be8bd8b4a21db3dcf48667427087792c
  flutter_discord_rpc: 53b006f68ef620a99fe1b3ba7e83513f3ae95b4c
  flutter_window_close: bd408414cbbf0d39f0d3076c4da0cdbf1c527168
  FlutterMacOS: 8f6f14fa908a6fb3fba0cd85dbd81ec4b251fb24
  fvp: 89275776efede62b7fde1762afa518974e6d6750
  macos_window_utils: 3bca8603c2a1cf2257351dfe6bbccc9accf739fd
  mdk: a726c85fd49c002b83d586b278d452f387750889
  path_provider_foundation: 080d55be775b7414fd5a5ef3ac137b97b097e564
  shared_preferences_foundation: 9e1978ff2562383bd5676f64ec4e9aa8fa06a6f7
  url_launcher_macos: 0fba8ddabfc33ce0a9afe7c5fef5aab3d8d2d673
  video_player_avfoundation: 2cef49524dd1f16c5300b9cd6efd9611ce03639b

PODFILE CHECKSUM: 864efca2071e997fd7028c12bc4746eb1ba0eb14

COCOAPODS: 1.16.2
