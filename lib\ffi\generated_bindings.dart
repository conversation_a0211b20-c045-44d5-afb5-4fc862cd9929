// ignore_for_file: always_specify_types
// ignore_for_file: camel_case_types
// ignore_for_file: non_constant_identifier_names
// ignore_for_file: unused_field
// ignore_for_file: unused_element

// AUTO GENERATED FILE, DO NOT EDIT.
//
// Generated by `package:ffigen`.
// ignore_for_file: type=lint
import 'dart:ffi' as ffi;

/// Bindings to `lib/ffi/libmtorrentserver.h`.
class TorrentLibrary {
  /// Holds the symbol lookup function.
  final ffi.Pointer<T> Function<T extends ffi.NativeType>(String symbolName)
      _lookup;

  /// The symbols are looked up in [dynamicLibrary].
  TorrentLibrary(ffi.DynamicLibrary dynamicLibrary)
      : _lookup = dynamicLibrary.lookup;

  /// The symbols are looked up with [lookup].
  TorrentLibrary.fromLookup(
      ffi.Pointer<T> Function<T extends ffi.NativeType>(String symbolName)
          lookup)
      : _lookup = lookup;

  Start_return Start(
    ffi.Pointer<ffi.Char> mcfg,
  ) {
    return _Start(
      mcfg,
    );
  }

  late final _StartPtr =
      _lookup<ffi.NativeFunction<Start_return Function(ffi.Pointer<ffi.Char>)>>(
          'Start');
  late final _Start =
      _StartPtr.asFunction<Start_return Function(ffi.Pointer<ffi.Char>)>();
}

final class max_align_t extends ffi.Opaque {}

final class _GoString_ extends ffi.Struct {
  external ffi.Pointer<ffi.Char> p;

  @ptrdiff_t()
  external int n;
}

typedef ptrdiff_t = ffi.Long;
typedef Dartptrdiff_t = int;

final class GoInterface extends ffi.Struct {
  external ffi.Pointer<ffi.Void> t;

  external ffi.Pointer<ffi.Void> v;
}

final class GoSlice extends ffi.Struct {
  external ffi.Pointer<ffi.Void> data;

  @GoInt()
  external int len;

  @GoInt()
  external int cap;
}

typedef GoInt = GoInt64;
typedef GoInt64 = ffi.LongLong;
typedef DartGoInt64 = int;

/// Return type for Start
final class Start_return extends ffi.Struct {
  @GoInt()
  external int r0;

  external ffi.Pointer<ffi.Char> r1;
}

const int NULL = 0;
