name: unyo
version: '0.1.2'
summary: An Anime app
description: Anime app.

base: core22
confinement: strict
grade: stable

apps:
  my-flutter-app:
    command: unyo
    extensions: [gnome]

parts:
  unyo:
    source: .
    plugin: flutter
    flutter-target: lib/main.dart

    # This appears to be needed when building in the Snap Store.
    build-packages:
      - curl

# Limit the architectures to build for to prevent unnecessary builds on
# architectures that Flutter doesn't support.
architectures:
  - build-on: [ amd64 ]
  - build-on: [ arm64 ]
